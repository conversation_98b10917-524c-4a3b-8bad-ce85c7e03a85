// Cookie Popup for Index Page Only
document.addEventListener("DOMContentLoaded", () => {
  const cookiePopup = document.getElementById("cookiePopup")

  if (!cookiePopup) {
    console.warn("Cookie popup element not found")
    return
  }

  // Ensure popup is hidden initially
  cookiePopup.style.display = "none"
  cookiePopup.classList.remove("show")

  // Show cookie popup with animation
  function showCookiePopup() {
    cookiePopup.style.display = "block"
    // Trigger animation after display
    setTimeout(() => {
      cookiePopup.classList.add("show")
    }, 100)
  }

  // Hide cookie popup with animation
  function hideCookiePopup() {
    cookiePopup.classList.remove("show")
    // Hide after animation completes
    setTimeout(() => {
      cookiePopup.style.display = "none"
    }, 300)
  }

  // Check if cookie consent has been given
  function checkCookieConsent() {
    const consent = localStorage.getItem("cookieConsent")
    const dismissed = sessionStorage.getItem("cookiePopupDismissed")

    // Always ensure popup is hidden initially
    hideCookiePopup()

    if (!consent && !dismissed) {
      // Show popup after age gate is handled (if any) and after a delay
      setTimeout(() => {
        // Check if age gate is still visible
        const ageGate = document.getElementById("ageGateModal")
        if (!ageGate || !ageGate.classList.contains("show")) {
          showCookiePopup()
        } else {
          // Wait for age gate to be dismissed, then show cookie popup
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                if (!ageGate.classList.contains("show")) {
                  setTimeout(() => showCookiePopup(), 500)
                  observer.disconnect()
                }
              }
            })
          })
          observer.observe(ageGate, { attributes: true })
        }
      }, 3000) // 3 second delay
    } else if (consent) {
      applyCookieSettings(JSON.parse(consent))
    }
  }

  // Save cookie consent
  function saveCookieConsent(preferences) {
    const consent = {
      essential: true, // Always true
      analytics: preferences.analytics || false,
      functional: preferences.functional || false,
      timestamp: new Date().toISOString(),
    }

    localStorage.setItem("cookieConsent", JSON.stringify(consent))
    applyCookieSettings(consent)
    hideCookiePopup()
  }

  // Apply cookie settings
  function applyCookieSettings(consent) {
    // Essential cookies are always enabled
    
    // Analytics cookies
    if (consent.analytics) {
      enableAnalyticsCookies()
    } else {
      disableAnalyticsCookies()
    }

    // Functional cookies
    if (consent.functional) {
      enableFunctionalCookies()
    } else {
      disableFunctionalCookies()
    }
  }

  // Enable analytics cookies
  function enableAnalyticsCookies() {
    // Add Google Analytics or other analytics code here
    console.log("Analytics cookies enabled")
  }

  // Disable analytics cookies
  function disableAnalyticsCookies() {
    // Remove analytics cookies and tracking
    console.log("Analytics cookies disabled")
  }

  // Enable functional cookies
  function enableFunctionalCookies() {
    // Enable functional features
    console.log("Functional cookies enabled")
  }

  // Disable functional cookies
  function disableFunctionalCookies() {
    // Disable functional features
    console.log("Functional cookies disabled")
  }

  // Event listeners
  const acceptButton = document.getElementById("acceptCookies")
  const rejectButton = document.getElementById("rejectCookies")
  const closeButton = document.getElementById("closeCookiePopup")

  if (acceptButton) {
    acceptButton.addEventListener("click", () => {
      saveCookieConsent({
        analytics: true,
        functional: true,
      })
    })
  }

  if (rejectButton) {
    rejectButton.addEventListener("click", () => {
      saveCookieConsent({
        analytics: false,
        functional: false,
      })
    })
  }

  if (closeButton) {
    closeButton.addEventListener("click", () => {
      // Store in sessionStorage to prevent showing again in this session
      sessionStorage.setItem("cookiePopupDismissed", "true")
      hideCookiePopup()
    })
  }

  // Initialize
  checkCookieConsent()
})
