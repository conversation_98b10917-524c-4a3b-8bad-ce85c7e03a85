// Main JavaScript functionality
document.addEventListener("DOMContentLoaded", () => {
  // Page loading animation
  const pageLoader = document.createElement('div');
  pageLoader.className = 'page-loader';
  pageLoader.innerHTML = '<div class="loader-spinner"></div>';
  document.body.appendChild(pageLoader);

  // Hide loader after page loads
  window.addEventListener('load', () => {
    setTimeout(() => {
      pageLoader.classList.add('hidden');
      setTimeout(() => {
        pageLoader.remove();
      }, 500);
    }, 500);
  });

  // Intersection Observer for animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const animatedElements = document.querySelectorAll('.section-content, .feature-card, .result-card');
  animatedElements.forEach(el => {
    observer.observe(el);
  });

  // Add fade-in classes to feature cards with delays
  const featureCards = document.querySelectorAll('.feature-card');
  featureCards.forEach((card, index) => {
    card.classList.add('fade-in', `delay-${index + 1}`);
  });

  // Interactive lottery numbers
  const lotteryNumbers = document.querySelectorAll('.number');
  lotteryNumbers.forEach(number => {
    number.addEventListener('click', () => {
      // Create sparkle effect
      createSparkleEffect(number);
    });
  });

  // Sparkle effect function
  function createSparkleEffect(element) {
    const sparkle = document.createElement('div');
    sparkle.style.position = 'absolute';
    sparkle.style.width = '4px';
    sparkle.style.height = '4px';
    sparkle.style.background = '#ffd700';
    sparkle.style.borderRadius = '50%';
    sparkle.style.pointerEvents = 'none';
    sparkle.style.zIndex = '1000';

    const rect = element.getBoundingClientRect();
    sparkle.style.left = (rect.left + rect.width / 2) + 'px';
    sparkle.style.top = (rect.top + rect.height / 2) + 'px';

    document.body.appendChild(sparkle);

    // Animate sparkle
    sparkle.animate([
      { transform: 'scale(0) rotate(0deg)', opacity: 1 },
      { transform: 'scale(1.5) rotate(180deg)', opacity: 0 }
    ], {
      duration: 600,
      easing: 'ease-out'
    }).onfinish = () => sparkle.remove();
  }

  // Hero element reference for optimized parallax
  const hero = document.querySelector('.hero');

  // Theme toggle functionality
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = document.querySelector('.theme-icon');

  // Load saved theme or default to light
  const savedTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', savedTheme);
  updateThemeIcon(savedTheme);

  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
      updateThemeIcon(newTheme);

      // Add a subtle animation
      themeToggle.style.transform = 'scale(0.9)';
      setTimeout(() => {
        themeToggle.style.transform = 'scale(1)';
      }, 150);
    });
  }

  function updateThemeIcon(theme) {
    if (themeIcon) {
      themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
      // Update aria-pressed state
      themeToggle.setAttribute('aria-pressed', theme === 'dark');
    }
  }

  // Enhanced mobile menu accessibility
  const navToggle = document.getElementById("navToggle");
  const navMenu = document.getElementById("navMenu");

  if (navToggle && navMenu) {
    navToggle.addEventListener("click", () => {
      const isExpanded = navMenu.classList.contains("active");

      // Toggle menu
      navMenu.classList.toggle("active");

      // Update ARIA attributes
      navToggle.setAttribute("aria-expanded", !isExpanded);
      navMenu.setAttribute("aria-hidden", isExpanded);

      // Animate hamburger menu
      const spans = navToggle.querySelectorAll("span");
      spans.forEach((span, index) => {
        if (!isExpanded) {
          if (index === 0) span.style.transform = "rotate(45deg) translate(5px, 5px)";
          if (index === 1) span.style.opacity = "0";
          if (index === 2) span.style.transform = "rotate(-45deg) translate(7px, -6px)";
        } else {
          span.style.transform = "none";
          span.style.opacity = "1";
        }
      });

      // Focus management
      if (!isExpanded) {
        // Focus first menu item when opening
        const firstMenuItem = navMenu.querySelector('.nav-link');
        if (firstMenuItem) {
          setTimeout(() => firstMenuItem.focus(), 100);
        }
      }
    });

    // Close menu with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && navMenu.classList.contains('active')) {
        navMenu.classList.remove('active');
        navToggle.setAttribute("aria-expanded", "false");
        navMenu.setAttribute("aria-hidden", "true");
        navToggle.focus();

        // Reset hamburger menu
        const spans = navToggle.querySelectorAll("span");
        spans.forEach((span) => {
          span.style.transform = "none";
          span.style.opacity = "1";
        });
      }
    });

    // Close mobile menu when clicking on a link
    const navLinks = document.querySelectorAll(".nav-link, .nav-actions-mobile .btn");
    navLinks.forEach((link) => {
      link.addEventListener("click", () => {
        if (navMenu.classList.contains("active")) {
          navMenu.classList.remove("active");
          navToggle.setAttribute("aria-expanded", "false");
          navMenu.setAttribute("aria-hidden", "true");

          // Reset hamburger menu
          const spans = navToggle.querySelectorAll("span");
          spans.forEach((span) => {
            span.style.transform = "none";
            span.style.opacity = "1";
          });
        }
      });
    });
  }

  // Announce page changes for screen readers
  function announcePageChange(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);

    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  // Keyboard navigation for lottery numbers
  const lotteryNumbers = document.querySelectorAll('.number');
  lotteryNumbers.forEach((number, index) => {
    number.setAttribute('tabindex', '0');
    number.setAttribute('role', 'button');
    number.setAttribute('aria-label', `Lottery number ${number.textContent}`);

    number.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        number.click();
        announcePageChange(`Selected lottery number ${number.textContent}`);
      }
    });
  });

  // Performance optimizations

  // Debounced scroll handler for better performance
  let scrollTimeout;
  function handleScroll() {
    if (scrollTimeout) {
      cancelAnimationFrame(scrollTimeout);
    }

    scrollTimeout = requestAnimationFrame(() => {
      // Parallax effect for hero section (optimized)
      if (hero && window.pageYOffset < window.innerHeight) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3; // Reduced for better performance
        hero.style.transform = `translate3d(0, ${rate}px, 0)`;
      }
    });
  }

  // Replace the old parallax code with optimized version
  if (hero) {
    window.addEventListener('scroll', handleScroll, { passive: true });
  }

  // Intersection Observer for performance-optimized animations
  const performanceObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.willChange = 'transform, opacity';
        entry.target.classList.add('visible');

        // Remove will-change after animation completes
        setTimeout(() => {
          entry.target.style.willChange = 'auto';
        }, 1000);

        performanceObserver.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  });

  // Observe elements with performance optimization
  const elementsToAnimate = document.querySelectorAll('.section-content, .feature-card, .result-card');
  elementsToAnimate.forEach(el => {
    performanceObserver.observe(el);
  });

  // Preload critical resources
  function preloadCriticalResources() {
    // Preload fonts if any are used
    const fontPreload = document.createElement('link');
    fontPreload.rel = 'preload';
    fontPreload.as = 'font';
    fontPreload.type = 'font/woff2';
    fontPreload.crossOrigin = 'anonymous';

    // Add to head if font files exist
    // document.head.appendChild(fontPreload);
  }

  // Call preload function
  preloadCriticalResources();

  // Animated counters for statistics
  function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-target'));
      const increment = target / 100;
      let current = 0;

      const updateCounter = () => {
        if (current < target) {
          current += increment;
          if (counter.textContent.includes('$')) {
            counter.textContent = '$' + Math.ceil(current).toLocaleString();
          } else if (counter.textContent.includes('%')) {
            counter.textContent = Math.ceil(current) + '%';
          } else {
            counter.textContent = Math.ceil(current).toLocaleString();
          }
          requestAnimationFrame(updateCounter);
        } else {
          if (counter.textContent.includes('$')) {
            counter.textContent = '$' + target.toLocaleString();
          } else if (counter.textContent.includes('%')) {
            counter.textContent = target + '%';
          } else {
            counter.textContent = target.toLocaleString();
          }
        }
      };

      updateCounter();
    });
  }



  // Trigger counter animation when stats section is visible
  const statsSection = document.querySelector('.stats-section');
  if (statsSection) {
    // Force immediate animation for debugging
    setTimeout(() => {
      animateCounters();
    }, 1000);

    const statsObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounters();
          statsObserver.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 }); // Reduced threshold

    statsObserver.observe(statsSection);
  }

  // Countdown Timer Functionality
  function initCountdownTimer() {
    const countdownTimer = document.getElementById('countdownTimer');
    if (!countdownTimer) return;

    // Set next draw date (Saturday 8:30 PM AEST)
    function getNextDrawDate() {
      const now = new Date();
      const nextSaturday = new Date();

      // Find next Saturday
      const daysUntilSaturday = (6 - now.getDay()) % 7;
      if (daysUntilSaturday === 0 && now.getHours() >= 20 && now.getMinutes() >= 30) {
        // If it's Saturday after 8:30 PM, get next Saturday
        nextSaturday.setDate(now.getDate() + 7);
      } else {
        nextSaturday.setDate(now.getDate() + (daysUntilSaturday || 7));
      }

      nextSaturday.setHours(20, 30, 0, 0); // 8:30 PM
      return nextSaturday;
    }

    const targetDate = getNextDrawDate();

    // Update draw date display
    const drawDateElement = document.getElementById('drawDate');
    if (drawDateElement) {
      drawDateElement.textContent = targetDate.toLocaleDateString('en-AU', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }

    function updateCountdown() {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance < 0) {
        // Draw has passed, get next draw
        const newTargetDate = getNextDrawDate();
        targetDate.setTime(newTargetDate.getTime());
        return;
      }

      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      // Update display
      const daysElement = document.getElementById('days');
      const hoursElement = document.getElementById('hours');
      const minutesElement = document.getElementById('minutes');
      const secondsElement = document.getElementById('seconds');

      if (daysElement) daysElement.textContent = days.toString().padStart(2, '0');
      if (hoursElement) hoursElement.textContent = hours.toString().padStart(2, '0');
      if (minutesElement) minutesElement.textContent = minutes.toString().padStart(2, '0');
      if (secondsElement) secondsElement.textContent = seconds.toString().padStart(2, '0');
    }

    // Update immediately and then every second
    updateCountdown();
    const countdownInterval = setInterval(updateCountdown, 1000);

    // Clean up interval on page unload
    window.addEventListener('beforeunload', () => {
      clearInterval(countdownInterval);
    });
  }

  // Initialize countdown timer
  initCountdownTimer();

  // FAQ Accordion Functionality
  function initFAQAccordion() {
    const faqQuestions = document.querySelectorAll('.faq-question');

    if (faqQuestions.length === 0) {
      return;
    }

    faqQuestions.forEach((question) => {
      question.addEventListener('click', (e) => {
        e.preventDefault();

        const answer = question.nextElementSibling;
        const isExpanded = question.getAttribute('aria-expanded') === 'true';

        // Close all other FAQ items
        faqQuestions.forEach(otherQuestion => {
          if (otherQuestion !== question) {
            otherQuestion.setAttribute('aria-expanded', 'false');
            otherQuestion.nextElementSibling.classList.remove('active');
          }
        });

        // Toggle current FAQ item
        question.setAttribute('aria-expanded', !isExpanded);
        answer.classList.toggle('active');
      });
    });
  }

  // Initialize FAQ accordion
  initFAQAccordion();

  // Memory cleanup on page unload
  window.addEventListener('beforeunload', () => {
    // Clean up observers
    if (observer) observer.disconnect();
    if (performanceObserver) performanceObserver.disconnect();

    // Remove event listeners
    window.removeEventListener('scroll', handleScroll);
  });


  // Smooth scrolling for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]')
  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      const href = this.getAttribute("href")
      if (href === "#") return

      const target = document.querySelector(href)
      if (target) {
        e.preventDefault()
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        })
      }
    })
  })

  // Add loading states to buttons
  const buttons = document.querySelectorAll(".btn")
  buttons.forEach((button) => {
    button.addEventListener("click", function () {
      // Don't add loading state to navigation buttons
      if (this.closest(".nav") || this.getAttribute("href")) return

      const originalText = this.textContent
      this.textContent = "Loading..."
      this.disabled = true

      // Reset after 2 seconds (for demo purposes)
      setTimeout(() => {
        this.textContent = originalText
        this.disabled = false
      }, 2000)
    })
  })

  // Form validation enhancement
  const forms = document.querySelectorAll("form")
  forms.forEach((form) => {
    form.addEventListener("submit", (e) => {
      const requiredFields = form.querySelectorAll("[required]")
      let isValid = true

      requiredFields.forEach((field) => {
        if (!field.value.trim()) {
          isValid = false
          field.style.borderColor = "#ef4444"
          field.style.backgroundColor = "#fef2f2"
        } else {
          field.style.borderColor = "#d1d5db"
          field.style.backgroundColor = "white"
        }
      })

      if (!isValid) {
        e.preventDefault()

        // Show error message
        let errorDiv = form.querySelector(".form-error")
        if (!errorDiv) {
          errorDiv = document.createElement("div")
          errorDiv.className = "form-error error-message"
          errorDiv.style.display = "block"
          form.insertBefore(errorDiv, form.firstChild)
        }
        errorDiv.textContent = "Please fill in all required fields."

        // Scroll to first invalid field
        const firstInvalid = form.querySelector(
          '[required]:invalid, [required][style*="border-color: rgb(239, 68, 68)"]',
        )
        if (firstInvalid) {
          firstInvalid.scrollIntoView({ behavior: "smooth", block: "center" })
          firstInvalid.focus()
        }
      }
    })

    // Remove error styling on input
    const inputs = form.querySelectorAll("input, select, textarea")
    inputs.forEach((input) => {
      input.addEventListener("input", function () {
        if (this.value.trim()) {
          this.style.borderColor = "#d1d5db"
          this.style.backgroundColor = "white"

          // Hide form error if all fields are now valid
          const errorDiv = form.querySelector(".form-error")
          if (errorDiv) {
            const invalidFields = form.querySelectorAll("[required]")
            let allValid = true
            invalidFields.forEach((field) => {
              if (!field.value.trim()) allValid = false
            })
            if (allValid) {
              errorDiv.style.display = "none"
            }
          }
        }
      })
    })
  })

  // Keyboard navigation for modals
  document.addEventListener("keydown", (e) => {
    // Close modals with Escape key
    if (e.key === "Escape") {
      const openModals = document.querySelectorAll('.modal-overlay[style*="flex"]')
      openModals.forEach((modal) => {
        modal.style.display = "none"
      })
    }

    // Trap focus in modals
    const openModal = document.querySelector('.modal-overlay[style*="flex"]')
    if (openModal && e.key === "Tab") {
      const focusableElements = openModal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      )
      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement.focus()
        }
      }
    }
  })

  // Accessibility improvements
  // Add ARIA labels to interactive elements
  const interactiveElements = document.querySelectorAll("button, a, input, select")
  interactiveElements.forEach((element) => {
    if (!element.getAttribute("aria-label") && !element.getAttribute("aria-labelledby")) {
      const text = element.textContent || element.value || element.alt
      if (text && text.trim()) {
        element.setAttribute("aria-label", text.trim())
      }
    }
  })

  // Add skip link functionality
  const skipLink = document.createElement("a")
  skipLink.href = "#main-content"
  skipLink.textContent = "Skip to main content"
  skipLink.className = "sr-only"
  skipLink.style.position = "absolute"
  skipLink.style.top = "10px"
  skipLink.style.left = "10px"
  skipLink.style.zIndex = "9999"
  skipLink.style.background = "#000"
  skipLink.style.color = "#fff"
  skipLink.style.padding = "8px 16px"
  skipLink.style.textDecoration = "none"
  skipLink.style.borderRadius = "4px"

  skipLink.addEventListener("focus", function () {
    this.style.position = "absolute"
    this.style.width = "auto"
    this.style.height = "auto"
    this.style.clip = "auto"
  })

  skipLink.addEventListener("blur", function () {
    this.className = "sr-only"
  })

  document.body.insertBefore(skipLink, document.body.firstChild)

  // Add main content ID if it doesn't exist
  const main = document.querySelector("main") || document.querySelector(".hero, .page-hero")
  if (main && !main.id) {
    main.id = "main-content"
  }

  // Lazy loading for images (if any are added later)
  const images = document.querySelectorAll("img[data-src]")
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target
          img.src = img.dataset.src
          img.removeAttribute("data-src")
          observer.unobserve(img)
        }
      })
    })

    images.forEach((img) => imageObserver.observe(img))
  } else {
    // Fallback for older browsers
    images.forEach((img) => {
      img.src = img.dataset.src
      img.removeAttribute("data-src")
    })
  }

  // Performance monitoring
  if ("performance" in window) {
    window.addEventListener("load", () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType("navigation")[0]
        if (perfData) {
          console.log("Page load time:", perfData.loadEventEnd - perfData.loadEventStart, "ms")
        }
      }, 0)
    })
  }

  // Error handling for external links
  const externalLinks = document.querySelectorAll('a[target="_blank"]')
  externalLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      try {
        // Add rel="noopener noreferrer" for security
        if (!this.rel.includes("noopener")) {
          this.rel += " noopener noreferrer"
        }
      } catch (error) {
        console.warn("Could not update external link security attributes:", error)
      }
    })
  })

  // Console warning for developers
  console.log("%cAustralian Official Lottery", "color: #2563eb; font-size: 24px; font-weight: bold;")
  console.log("%c18+ Only - Gambling can be addictive", "color: #dc2626; font-size: 16px; font-weight: bold;")
  console.log("%cHelp: 1800 858 858", "color: #16a34a; font-size: 14px;")
})

// Utility functions
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

function throttle(func, limit) {
  let inThrottle
  return function () {
    const args = arguments
    
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// Export functions for potential use in other scripts
window.LotteryUtils = {
  debounce,
  throttle,
}

// Global FAQ toggle function
window.toggleFAQ = function(questionElement) {
  const answer = questionElement.nextElementSibling;
  const isExpanded = questionElement.getAttribute('aria-expanded') === 'true';

  // Close all other FAQ items
  const allQuestions = document.querySelectorAll('.faq-question');
  allQuestions.forEach(otherQuestion => {
    if (otherQuestion !== questionElement) {
      otherQuestion.setAttribute('aria-expanded', 'false');
      otherQuestion.nextElementSibling.classList.remove('active');
    }
  });

  // Toggle current FAQ item
  questionElement.setAttribute('aria-expanded', !isExpanded);
  answer.classList.toggle('active');
};
