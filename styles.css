
/*
===============================================
KOOALA LOTTO - NOTION-STYLE BLACK & WHITE THEME
===============================================
*/

:root {
  /* Notion-inspired Color Palette */
  --color-white: #ffffff;
  --color-gray-50: #f9f9f9;
  --color-gray-100: #f1f1f1;
  --color-gray-200: #e6e6e6;
  --color-gray-300: #d1d1d1;
  --color-gray-400: #b4b4b4;
  --color-gray-500: #8b8b8b;
  --color-gray-600: #6b6b6b;
  --color-gray-700: #4a4a4a;
  --color-gray-800: #2f2f2f;
  --color-gray-900: #1a1a1a;
  --color-black: #000000;

  /* Primary Colors */
  --primary-color: var(--color-black);
  --secondary-color: var(--color-gray-600);
  --accent-color: var(--color-gray-800);

  /* Text Colors */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-600);
  --text-muted: var(--color-gray-500);
  --text-inverse: var(--color-white);

  /* Background Colors */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-card: var(--color-white);

  /* Border Colors */
  --border-light: var(--color-gray-200);
  --border-medium: var(--color-gray-300);
  --border-dark: var(--color-gray-400);

  /* Spacing (Notion-style) */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* Border Radius (Notion-style) */
  --radius-sm: 3px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;

  /* Shadows (Notion-style) */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  --font-size-5xl: 48px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/*
===============================================
RESET AND BASE STYLES
===============================================
*/

/* CSS Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--text-secondary);
}

ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/*
===============================================
COMPONENTS
===============================================
*/

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--color-gray-400);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
}

.btn-primary:hover {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-800);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-black);
  border-color: var(--color-gray-300);
}

.btn-outline:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.btn-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  border-color: var(--color-gray-200);
}

.btn-secondary:hover {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-300);
}

.btn-cta {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

.btn-cta:hover {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Responsible Gaming Banner */
.responsible-banner {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-sm) 0;
  position: relative;
  border-bottom: 1px solid var(--border-light);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.banner-icon {
  font-size: var(--font-size-base);
}

.banner-link {
  color: var(--color-white);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
  transition: opacity var(--transition-fast);
}

.banner-link:hover {
  opacity: 0.8;
}

/* Header */
.header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  position: relative;
  z-index: 10;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  position: relative;
}

.logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary) !important;
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary);
}

.nav-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  z-index: 10;
  position: relative;
}

.nav-actions-desktop {
  display: flex;
}

.nav-actions-mobile {
  display: none;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

/* Fix button visibility in header - Force visible styles */
.nav-actions .btn,
.nav-actions .btn-primary,
.nav-actions .btn-secondary {
  background: var(--color-black) !important;
  color: var(--color-white) !important;
  border: 2px solid var(--color-black) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-size: var(--font-size-sm) !important;
  border-radius: var(--radius-md) !important;
  transition: all 0.3s ease !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 80px !important;
  text-decoration: none !important;
  cursor: pointer !important;
}

.nav-actions .btn-outline {
  background: var(--color-white) !important;
  color: var(--color-black) !important;
  border: 2px solid var(--color-black) !important;
}

.nav-actions .btn:hover,
.nav-actions .btn-primary:hover,
.nav-actions .btn-secondary:hover {
  background: var(--color-gray-800) !important;
  color: var(--color-white) !important;
  border-color: var(--color-gray-800) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
}

.nav-actions .btn-outline:hover {
  background: var(--color-black) !important;
  color: var(--color-white) !important;
  border-color: var(--color-black) !important;
}

/* Mobile Navigation Toggle */
.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
}

.nav-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
}

/* Age Gate Modal Styles */
.age-gate-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  display: none; /* Hidden by default */
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.age-gate-modal.show {
  display: flex; /* Show when needed */
}

.age-gate-content {
  background: var(--bg-primary);
  padding: var(--spacing-3xl);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
}

.age-gate-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.age-gate-header h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-semibold);
}

.age-gate-header p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.birth-date-inputs {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.input-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.input-group select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.input-group select:focus {
  outline: none;
  border-color: var(--color-gray-600);
}

.age-error {
  background-color: var(--color-gray-50);
  color: var(--color-gray-800);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  text-align: center;
  border: 1px solid var(--border-medium);
  font-size: var(--font-size-sm);
}

.age-gate-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.age-gate-buttons .btn {
  flex: 1;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.age-gate-footer {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Cookie Banner Styles - moved to line 6654 for better organization */

.cookie-content {
  padding: var(--spacing-lg);
}

.cookie-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.cookie-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.cookie-icon {
  font-size: var(--font-size-base);
}

.cookie-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  transition: color var(--transition-fast);
}

.cookie-close:hover {
  color: var(--text-primary);
}

.cookie-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

.cookie-text a {
  color: var(--text-primary);
  text-decoration: underline;
}

.cookie-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.cookie-buttons .btn {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

/*
===============================================
MAIN SECTIONS
===============================================
*/

/* Hero Section */
.hero {
  background-color: var(--bg-primary);
  padding: var(--spacing-3xl) 0;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.hero-content h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-weight: var(--font-weight-medium);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-relaxed);
}

/* Next Draw Section */
.next-draw-section {
  background-color: var(--bg-secondary);
  padding: var(--spacing-3xl) 0;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.draw-content {
  max-width: 800px;
  margin: 0 auto;
}

.draw-content h2 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
}

.draw-subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-2xl);
  color: var(--text-secondary);
}

.draw-info {
  margin-bottom: var(--spacing-2xl);
}

.jackpot-amount {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.jackpot-amount .currency {
  font-size: var(--font-size-3xl);
}

.jackpot-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.timer-unit {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  min-width: 100px;
  box-shadow: var(--shadow-sm);
}

.timer-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
  color: var(--text-primary);
}

.timer-label {
  display: block;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  color: var(--text-secondary);
}

.draw-details {
  margin-bottom: var(--spacing-2xl);
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.draw-details p {
  margin-bottom: var(--spacing-xs);
}

/* Countdown Timer with ID */
#countdown-timer {
  margin-bottom: var(--spacing-2xl);
}

#countdown-timer .countdown-container {
  text-align: center;
}

#countdown-timer .countdown-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

#countdown-timer .countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

#countdown-timer .countdown-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

#countdown-timer .countdown-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  min-width: 60px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  text-align: center;
}

#countdown-timer .countdown-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#countdown-timer .countdown-separator {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-secondary);
  margin: 0 var(--spacing-xs);
}

#countdown-timer .countdown-status {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-md);
}

/* Section Styles */
.section-content {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  max-width: 1200px;
  margin: 0 auto;
  opacity: 1;
  visibility: visible;
}

.section-content h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
}

/* Stats Section */
.stats-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.stats-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.stats-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Info Section */
.info-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.info-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.info-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/* Jackpots Section */
.jackpots-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.jackpots-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.jackpots-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.jackpots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.jackpot-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.jackpot-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.jackpot-card.featured {
  border-color: var(--color-gray-600);
  box-shadow: var(--shadow-md);
}

.jackpot-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.draw-date {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.jackpot-card .jackpot-amount {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0;
}

.jackpot-info p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.testimonials-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.testimonials-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.testimonial-card {
  background-color: var(--bg-card);
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.testimonial-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.testimonial-content {
  margin-bottom: var(--spacing-lg);
}

.testimonial-content p {
  font-style: italic;
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.testimonial-author {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info h4 {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-base);
}

.author-info span {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.win-amount {
  background-color: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.testimonial-disclaimer {
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background-color: var(--color-gray-50);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  text-align: center;
}

.testimonial-disclaimer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* FAQ Section */
.faq-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.faq-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.faq-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.faq-notice {
  background-color: var(--color-gray-50);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  text-align: center;
}

.faq-notice h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-semibold);
}

.faq-notice p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.faq-notice a {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: underline;
}

.faq-grid {
  display: grid;
  gap: var(--spacing-md);
  margin-top: var(--spacing-2xl);
}

.faq-item {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  transition: box-shadow var(--transition-normal);
}

.faq-item:hover {
  box-shadow: var(--shadow-md);
}

.faq-question {
  width: 100%;
  padding: var(--spacing-lg);
  background: none;
  border: none;
  text-align: left;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-fast);
}

.faq-question:hover {
  background-color: var(--bg-secondary);
  transform: translateX(2px);
}

.faq-question:active {
  background-color: var(--color-gray-200);
}

.faq-question[aria-expanded="true"] {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}



.faq-icon {
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  transition: transform var(--transition-normal);
  font-weight: bold;
}

.faq-question[aria-expanded="true"] .faq-icon {
  transform: rotate(45deg);
  color: var(--text-primary);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.faq-answer.active {
  max-height: 500px;
}

.faq-answer p {
  padding: var(--spacing-lg);
  margin: 0;
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.faq-answer a {
  color: var(--text-primary);
  text-decoration: underline;
}

/* Responsible Section */
.responsible-section {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-gray-800);
  color: var(--color-white);
  text-align: center;
  opacity: 1;
  visibility: visible;
  display: block;
}

.responsible-section h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.responsible-section p {
  color: var(--color-gray-200);
  margin-bottom: var(--spacing-lg);
}

.help-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.help-links a {
  color: var(--color-white);
  text-decoration: underline;
}

.help-links span {
  color: var(--color-gray-400);
}

/*
===============================================
FOOTER
===============================================
*/

.footer {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-column h4 {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.footer-column p {
  color: var(--color-gray-300);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: var(--spacing-sm);
}

.footer-column a {
  color: var(--color-gray-300);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-column a:hover {
  color: var(--color-white);
}

.footer-regulators {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding-top: var(--spacing-2xl);
  border-top: 1px solid var(--color-gray-700);
}

.footer-regulators h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.regulator-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.regulator-icon {
  height: 40px;
  width: auto;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  background-color: var(--color-white);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.regulator-icon:hover {
  opacity: 1;
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-gray-700);
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
}

.footer-bottom p {
  margin-bottom: var(--spacing-xs);
  color: var(--color-gray-400);
}

.footer-badges {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.badge {
  background-color: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/*
===============================================
MOBILE RESPONSIVENESS
===============================================
*/

@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    flex-direction: column;
    gap: 0;
    padding: var(--spacing-lg);
    transition: top var(--transition-normal);
    z-index: var(--z-dropdown);
    display: none;
  }

  .nav-menu.active {
    display: flex;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-actions-desktop {
    display: none;
  }

  .nav-actions-mobile {
    display: flex;
  }

  /* Hero Section */
  .hero-content h1 {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  /* Timer */
  .countdown-timer {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .timer-unit {
    min-width: auto;
    padding: var(--spacing-md);
  }

  .timer-number {
    font-size: var(--font-size-2xl);
  }

  .jackpot-amount {
    font-size: var(--font-size-3xl);
  }

  /* Grids */
  .stats-grid,
  .features-grid,
  .testimonials-grid,
  .jackpots-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  /* Testimonials */
  .testimonial-author {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  /* Footer */
  .footer-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .regulator-icons {
    gap: var(--spacing-md);
  }

  .regulator-icon {
    height: 32px;
  }

  /* Help Links */
  .help-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  /* Age Gate */
  .birth-date-inputs {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .age-gate-buttons {
    flex-direction: column;
  }

  /* Cookie Popup */
  .cookie-popup {
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }

  .cookie-buttons {
    flex-direction: column;
  }
}

/*
===============================================
UTILITY CLASSES
===============================================
*/

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

/* Spacing Utilities */
.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flex Utilities */
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

/* Border Utilities */
.border { border: 1px solid var(--border-light); }
.border-medium { border: 1px solid var(--border-medium); }
.border-dark { border: 1px solid var(--border-dark); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Background Utilities */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-card { background-color: var(--bg-card); }

/*
===============================================
ANIMATIONS
===============================================
*/

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in { animation: fadeIn 0.5s ease-out; }
.slide-up { animation: slideUp 0.5s ease-out; }
.pulse { animation: pulse 2s infinite; }

/*
===============================================
PAGE-SPECIFIC STYLES
===============================================
*/

/* Page Hero Styles */
.page-hero {
  background-color: var(--bg-primary);
  padding: var(--spacing-3xl) 0;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.page-hero h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.page-hero p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-intro {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.about-intro h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about-intro .lead {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

.about-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.about-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.about-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/* Mission Section */
.mission-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.mission-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.mission-content h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.mission-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/* Results Section */
.results-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.results-grid {
  display: grid;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.result-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.draw-info h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.draw-info p {
  color: var(--text-muted);
  margin-bottom: 0;
}

.prize-info {
  text-align: right;
}

.prize-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.prize-amount {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: 0;
}

.result-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
}

.numbers-section h4,
.prize-section h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.winning-numbers {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.number {
  width: 40px;
  height: 40px;
  background-color: var(--color-gray-800);
  color: var(--color-white);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.number.supplementary {
  background-color: var(--color-gray-400);
}

.supplementary-section {
  margin-bottom: var(--spacing-lg);
}

.supplementary-section p {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-sm);
}

.supplementary-numbers {
  display: flex;
  gap: var(--spacing-sm);
}

.prize-details {
  display: grid;
  gap: var(--spacing-sm);
}

.prize-row {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light);
}

.prize-row:last-child {
  border-bottom: none;
}

.prize-row span:first-child {
  color: var(--text-secondary);
}

.prize-row span:last-child {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* Steps Section (How It Works) */
.steps-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.step-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: transform var(--transition-fast);
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: var(--color-black);
  color: var(--color-white);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.step-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0 var(--spacing-md);
}

.step-content p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.step-icon {
  font-size: var(--font-size-2xl);
  color: var(--color-gray-600);
}

/* Responsible Gaming Styles */
.responsible-hero {
  background-color: var(--color-gray-50);
}

.hero-icon {
  font-size: var(--font-size-5xl);
  margin-bottom: var(--spacing-lg);
}

.key-message-section {
  background-color: var(--color-gray-50);
  border: 1px solid var(--border-medium);
  padding: var(--spacing-2xl) 0;
}

.key-message {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.key-message h2 {
  color: var(--text-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
}

.message-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.recommendations-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.recommendation-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.rec-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.recommendation-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.recommendation-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/* Self Exclusion Section */
.self-exclusion-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-secondary);
}

.exclusion-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  margin-top: var(--spacing-2xl);
}

.exclusion-info h3 {
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: var(--spacing-2xl) 0 var(--spacing-md);
}

.exclusion-info h3:first-child {
  margin-top: 0;
}

.exclusion-info ul {
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
}

.exclusion-info li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
}

.action-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  height: fit-content;
  box-shadow: var(--shadow-sm);
}

.action-card h3 {
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.action-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
}

.action-card .btn {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing-md);
  text-decoration: none;
  text-align: center;
}

.action-card .btn:last-child {
  margin-bottom: 0;
}

/* Contact Section */
.contact-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  margin-top: var(--spacing-2xl);
}

.contact-info {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
}

.contact-info h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.contact-icon {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
}

.contact-details h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-details p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.contact-form {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
}

.contact-form h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

/* Auth Section */
.auth-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.auth-container {
  max-width: 500px;
  margin: 0 auto;
}

.auth-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3xl);
  box-shadow: var(--shadow-sm);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.auth-header h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.auth-header p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.auth-tabs {
  display: flex;
  margin-bottom: var(--spacing-2xl);
  border-bottom: 1px solid var(--border-light);
}

/* Tab button styles moved to line 3593 for better organization */

.auth-form {
  display: block; /* Forms should be visible within their tabs */
}

.auth-form.active {
  display: block;
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.auth-footer p {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

/* Security Section */
.security-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.security-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.security-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.security-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.security-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.security-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.security-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
  text-align: center;
}

/* Content Section */
.content-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
}

.content-section h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.content-section h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: var(--spacing-2xl) 0 var(--spacing-md);
}

.content-section h3:first-child {
  margin-top: 0;
}

.content-section p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.content-section ul {
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
}

.content-section li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

/* Advantages Section */
.advantages-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.advantages-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.advantages-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.advantage-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.advantage-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.advantage-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.advantage-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.advantage-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/* Security & Trust Section */
.security-trust-section {
  padding: var(--spacing-3xl) 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  opacity: 1;
  visibility: visible;
  display: block;
}

.security-trust-section h2 {
  color: var(--text-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.security-trust-section .section-description {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-3xl);
  text-align: center;
}

.security-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.security-feature {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.security-feature:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.security-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.security-feature h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.security-feature p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

/*
===============================================
FORMS
===============================================
*/

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-gray-600);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  color: var(--color-gray-700);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
}

/*
===============================================
TABLES
===============================================
*/

.table-container {
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  font-size: var(--font-size-sm);
}

.table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  border-bottom: 1px solid var(--border-medium);
}

.table td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
}

.table code {
  background-color: var(--color-gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-gray-800);
}

/* Responsible Gaming Banner */
.responsible-banner {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-sm) 0;
  position: relative;
  border-bottom: 1px solid var(--border-light);
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.banner-icon {
  font-size: var(--font-size-base);
}

.banner-link {
  color: var(--color-white);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
  transition: opacity var(--transition-fast);
}

.banner-link:hover {
  opacity: 0.8;
}



/* Navigation */
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary) !important;
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary);
}



/* Mobile Navigation Toggle */
.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
}

.nav-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
}

/* Footer Badges */
.footer-badges {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.badge {
  background-color: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* Age Gate Modal Styles - Duplicate removed, using styles from line 468 */

.age-gate-content {
  background: var(--bg-primary);
  padding: var(--spacing-3xl);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
}

.age-gate-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.age-gate-header h2 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-semibold);
}

.age-gate-header p {
  color: var(--text-secondary);
  margin-bottom: 0;
}

.birth-date-inputs {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.input-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.input-group select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.input-group select:focus {
  outline: none;
  border-color: var(--color-gray-600);
}

.age-error {
  background-color: var(--color-gray-50);
  color: var(--color-gray-800);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  text-align: center;
  border: 1px solid var(--border-medium);
  font-size: var(--font-size-sm);
}

.age-gate-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.age-gate-buttons .btn {
  flex: 1;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.age-gate-footer {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  line-height: var(--line-height-relaxed);
}

/* Cookie Banner Styles - consolidated to line 6634 */

.cookie-content {
  padding: var(--spacing-lg);
}

.cookie-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.cookie-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.cookie-icon {
  font-size: var(--font-size-base);
}

.cookie-close {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  transition: color var(--transition-fast);
}

.cookie-close:hover {
  color: var(--text-primary);
}

.cookie-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

.cookie-text a {
  color: var(--text-primary);
  text-decoration: underline;
}

.cookie-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.cookie-buttons .btn {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

/* Mobile responsiveness for cookie popup */
@media (max-width: 768px) {
  .cookie-popup {
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }

  .cookie-buttons {
    flex-direction: column;
  }
}

/*
===============================================
MAIN SECTIONS
===============================================
*/

/* Hero Section */
.hero {
  background-color: var(--bg-primary);
  padding: var(--spacing-3xl) 0;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.hero-content h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-weight: var(--font-weight-medium);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-relaxed);
}





.section-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
}









.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.testimonial-card {
  background-color: var(--bg-card);
  padding: var(--spacing-2xl);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.testimonial-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.testimonial-content {
  margin-bottom: var(--spacing-lg);
}

.testimonial-content p {
  font-style: italic;
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  margin-bottom: 0;
}

.testimonial-author {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info h4 {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-base);
}

.author-info span {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.win-amount {
  background-color: var(--color-gray-800);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.testimonial-disclaimer {
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-lg);
  background-color: var(--color-gray-50);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  text-align: center;
}

.testimonial-disclaimer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}



/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-2xl);
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}



.jackpots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.jackpot-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.jackpot-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.jackpot-card.featured {
  border-color: var(--color-gray-600);
  box-shadow: var(--shadow-md);
}

.jackpot-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.draw-date {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
}

.jackpot-card .jackpot-amount {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0;
}

.jackpot-info p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}



.faq-notice {
  background-color: var(--color-gray-50);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  text-align: center;
}

.faq-notice h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-semibold);
}

.faq-notice p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.faq-notice a {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: underline;
}





.responsible-section h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.responsible-section p {
  color: var(--color-gray-200);
  margin-bottom: var(--spacing-lg);
}

.help-links {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.help-links a {
  color: var(--color-white);
  text-decoration: underline;
}

.help-links span {
  color: var(--color-gray-400);
}

/*
===============================================
FOOTER
===============================================
*/

.footer {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-column h4 {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-md);
}

.footer-column p {
  color: var(--color-gray-300);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: var(--spacing-sm);
}

.footer-column a {
  color: var(--color-gray-300);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-column a:hover {
  color: var(--color-white);
}

.footer-regulators {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding-top: var(--spacing-2xl);
  border-top: 1px solid var(--color-gray-700);
}

.footer-regulators h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.regulator-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.regulator-icon {
  height: 40px;
  width: auto;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  background-color: var(--color-white);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.regulator-icon:hover {
  opacity: 1;
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-gray-700);
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
}

.footer-bottom p {
  margin-bottom: var(--spacing-xs);
  color: var(--color-gray-400);
}

/*
===============================================
MOBILE RESPONSIVENESS
===============================================
*/

@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    flex-direction: column;
    gap: 0;
    padding: var(--spacing-lg);
    transition: top var(--transition-normal);
    z-index: var(--z-dropdown);
    display: none;
  }

  .nav-menu.active {
    display: flex;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-actions-desktop {
    display: none;
  }

  .nav-actions-mobile {
    display: flex;
  }

  /* Hero Section */
  .hero-content h1 {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  /* Timer */
  .countdown-timer {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .timer-unit {
    min-width: auto;
    padding: var(--spacing-md);
  }

  .timer-number {
    font-size: var(--font-size-2xl);
  }

  .jackpot-amount {
    font-size: var(--font-size-3xl);
  }

  /* Grids */
  .stats-grid,
  .features-grid,
  .testimonials-grid,
  .jackpots-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  /* Testimonials */
  .testimonial-author {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  /* Footer */
  .footer-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .regulator-icons {
    gap: var(--spacing-md);
  }

  .regulator-icon {
    height: 32px;
  }

  /* Help Links */
  .help-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  /* Age Gate */
  .birth-date-inputs {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .age-gate-buttons {
    flex-direction: column;
  }
}

/*
===============================================
UTILITY CLASSES
===============================================
*/

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

/* Spacing Utilities */
.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Display Utilities */
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Flex Utilities */
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

/* Border Utilities */
.border { border: 1px solid var(--border-light); }
.border-medium { border: 1px solid var(--border-medium); }
.border-dark { border: 1px solid var(--border-dark); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Background Utilities */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-card { background-color: var(--bg-card); }

/*
===============================================
ANIMATIONS
===============================================
*/

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in { animation: fadeIn 0.5s ease-out; }
.slide-up { animation: slideUp 0.5s ease-out; }
.pulse { animation: pulse 2s infinite; }
  background: var(--primary-gradient);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.auth-container {
  max-width: 500px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.auth-header {
  text-align: center;
  padding: 2rem 2rem 1rem;
  background: var(--bg-secondary);
}

.auth-header h1 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.auth-tabs {
  display: flex;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.tab-button {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-button.active {
  color: var(--primary-color);
  background: white;
  border-bottom-color: var(--primary-color);
}

.tab-button:hover:not(.active) {
  background: var(--bg-secondary);
}

.auth-tab {
  display: none;
  padding: 2rem;
}

.auth-tab.active {
  display: block;
}

.auth-form h2 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.form-subtitle {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 0.95rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-help {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.checkbox-group {
  margin-bottom: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
  background: white;
  transition: all 0.3s ease;
}

.checkbox-label:hover .checkmark {
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.btn-full {
  width: 100%;
  padding: 0.875rem;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.auth-switch {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.link-button {
  background: none;
  border: none;
  color: var(--primary-color);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
}

.link-button:hover {
  opacity: 0.8;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.forgot-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-link:hover {
  text-decoration: underline;
}

.security-notice {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
}

.notice-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.notice-content h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.notice-content p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.notice-content a {
  color: var(--primary-color);
  text-decoration: none;
}

.notice-content a:hover {
  text-decoration: underline;
}

/* Success Modal */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.success-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-content h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.success-content p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Mobile responsiveness for auth */
@media (max-width: 768px) {
  .auth-container {
    margin: 1rem;
    max-width: none;
  }

  .auth-header,
  .auth-tab {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .security-notice {
    flex-direction: column;
    text-align: center;
  }
}

/* Contact Page Styles */
.contact-section {
  padding: 80px 0;
  background: var(--bg-primary);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
}

.contact-info h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.contact-info > p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-method {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.contact-method:hover {
  transform: translateY(-2px);
}

.method-icon {
  font-size: 2rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.method-details h3 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.method-details p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.response-time {
  color: var(--text-muted);
  font-size: 0.9rem;
  font-style: italic;
}

.contact-form-container {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.contact-form-container h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.contact-form .form-group {
  margin-bottom: 1.5rem;
}

.contact-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.contact-form input,
.contact-form select,
.contact-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.contact-form textarea {
  resize: vertical;
  min-height: 120px;
}

.regulatory-contacts {
  padding: 80px 0;
  background: #f8f9fa;
}

.regulatory-contacts h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 2rem;
}

.regulatory-contacts > p {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  font-size: 1.1rem;
}

.regulatory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.regulatory-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.regulatory-card:hover {
  transform: translateY(-3px);
}

.regulatory-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.regulatory-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.regulatory-card a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.regulatory-card a:hover {
  text-decoration: underline;
}

.emergency-support {
  padding: 60px 0;
  background: var(--color-black);
  color: var(--color-white);
  text-align: center;
}

.emergency-content h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.emergency-content p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.emergency-contacts {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.emergency-item {
  background: var(--bg-secondary);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.emergency-support .btn {
  background: white;
  color: var(--text-primary);
  border: none;
  padding: 0.75rem 2rem;
  font-weight: 600;
}

.emergency-support .btn:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

/* Mobile responsiveness for contact */
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-form-container {
    padding: 1.5rem;
  }

  .contact-method {
    flex-direction: column;
    text-align: center;
  }

  .method-icon {
    align-self: center;
  }

  .regulatory-grid {
    grid-template-columns: 1fr;
  }

  .emergency-contacts {
    flex-direction: column;
    align-items: center;
  }

  .emergency-item {
    width: 100%;
    max-width: 300px;
  }
}

/* About Page Styles */
.about-section {
  padding: 80px 0;
  background: var(--bg-primary);
}

.about-content {
  max-width: 1000px;
  margin: 0 auto;
}

.about-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.about-intro h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 2.5rem;
}

.about-intro .lead {
  font-size: 1.3rem;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.about-card {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.about-card:hover {
  transform: translateY(-5px);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.about-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.about-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}



.mission-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.mission-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.mission-content h2 {
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.mission-text {
  font-size: 1.3rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 3rem;
}

.mission-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  text-align: left;
}

.value-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.value-item h4 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.value-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.legal-notice {
  padding: 60px 0;
  background: var(--bg-secondary);
  border-top: 4px solid var(--border-dark);
  border-bottom: 4px solid var(--border-dark);
}

.notice-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.notice-content h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.notice-content p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.notice-content a {
  color: var(--text-primary);
  font-weight: 600;
  text-decoration: underline;
}

/* Security Page Styles */
.security-overview {
  padding: 80px 0;
  background: rgba(255, 255, 255, 0.95);
}

.security-intro {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.security-intro h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 2.5rem;
}

.security-intro .lead {
  font-size: 1.3rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.security-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.security-card {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.security-card:hover {
  transform: translateY(-5px);
}

.security-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.security-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

.security-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.data-protection {
  padding: 80px 0;
  background: #f8f9fa;
}

.data-protection h2 {
  color: var(--text-primary);
  margin-bottom: 3rem;
  text-align: center;
  font-size: 2.5rem;
}

.protection-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.protection-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.protection-item h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.protection-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.regulatory-compliance {
  padding: 80px 0;
  background: rgba(255, 255, 255, 0.95);
}

.regulatory-compliance h2 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 2.5rem;
}

.regulatory-compliance .section-description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  font-size: 1.2rem;
}

.compliance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.compliance-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.compliance-card:hover {
  transform: translateY(-3px);
}

.compliance-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.compliance-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.account-security {
  padding: 80px 0;
  background: #f8f9fa;
}

.account-security h2 {
  color: var(--text-primary);
  margin-bottom: 3rem;
  text-align: center;
  font-size: 2.5rem;
}

.security-tips {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.tip-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.tip-card h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.tip-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.contact-security {
  padding: 60px 0;
  background: var(--color-black);
  color: var(--color-white);
  text-align: center;
}

.contact-content h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.contact-content p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.security-contact {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.contact-method {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.contact-security .btn {
  background: white;
  color: var(--text-primary);
  border: none;
  padding: 0.75rem 2rem;
  font-weight: 600;
}

.contact-security .btn:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

/* Mobile responsiveness for additional pages */
@media (max-width: 768px) {
  .about-intro h2,
  .stats-section h2,
  .mission-content h2,
  .security-intro h2,
  .data-protection h2,
  .regulatory-compliance h2,
  .account-security h2 {
    font-size: 2rem;
  }

  .about-intro .lead,
  .mission-text,
  .security-intro .lead {
    font-size: 1.1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-number {
    font-size: 2rem;
  }

  .mission-values {
    grid-template-columns: 1fr;
  }

  .security-contact {
    flex-direction: column;
    align-items: center;
  }
}

/* FAQ Page Styles */
.important-notice {
  background: var(--bg-secondary);
  border: 3px solid var(--border-dark);
  border-radius: 16px;
  padding: 2.5rem;
  margin-bottom: 4rem;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.important-notice h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.notice-text {
  color: var(--text-secondary);
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.notice-text strong {
  font-weight: 700;
}

.notice-text a {
  color: var(--text-primary);
  font-weight: 600;
  text-decoration: underline;
}



.faq-categories {
  max-width: 900px;
  margin: 0 auto;
}

.faq-category {
  margin-bottom: 3rem;
}

.faq-category h2 {
  color: var(--text-primary);
  margin-bottom: 2rem;
  font-size: 2rem;
  padding-bottom: 0.5rem;
  border-bottom: 3px solid var(--primary-color);
}



.contact-support {
  padding: 60px 0;
  background: var(--primary-gradient);
  color: white;
  text-align: center;
}

.support-content h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.support-content p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.support-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.support-actions .btn {
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-decoration: none;
}

.support-actions .btn-primary {
  background: white;
  color: var(--text-primary);
  border: none;
}

.support-actions .btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.support-actions .btn-outline {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.support-actions .btn-outline:hover {
  background: white;
  color: var(--text-primary);
}

/* Mobile responsiveness for FAQ */
@media (max-width: 768px) {
  .important-notice {
    padding: 1.5rem;
    margin: 0 1rem 2rem;
    border-radius: 12px;
    overflow-x: hidden;
  }

  .important-notice h2 {
    font-size: 1.3rem;
    flex-direction: column;
    gap: 0.5rem;
    line-height: 1.3;
    word-wrap: break-word;
  }

  .notice-text {
    font-size: 1rem;
    line-height: 1.4;
    word-wrap: break-word;
  }

  .notice-text strong {
    display: block;
    margin-bottom: 0.5rem;
  }

  .faq-category h2 {
    font-size: 1.5rem;
  }

  .faq-question {
    padding: 1rem;
    font-size: 1rem;
  }

  .faq-answer p {
    padding: 1rem;
  }

  .support-actions {
    flex-direction: column;
    align-items: center;
  }

  .support-actions .btn {
    width: 100%;
    max-width: 300px;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--primary-gradient);
  background-attachment: fixed;
  min-height: 100vh;
}

/* Dark theme body background */
[data-theme="dark"] body {
  background: var(--color-black);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading animation */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-black);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.page-loader.hidden {
  opacity: 0;
  pointer-events: none;
}

.loader-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--color-black);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade in animation for content */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in.delay-1 { animation-delay: 0.1s; }
.fade-in.delay-2 { animation-delay: 0.2s; }
.fade-in.delay-3 { animation-delay: 0.3s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* Glass morphism effect for cards */
.glass-card {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
}

/*
===============================================
2. LAYOUT COMPONENTS
===============================================
*/

/* Age Badge - Fixed position age restriction indicator */
.age-badge {
  position: relative;
  background: var(--color-black);
  color: var(--color-white);
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 14px;
  display: inline-block;
  margin: 0.5rem;
}

/*
===============================================
3. HEADER AND NAVIGATION
===============================================
*/



.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s ease;
  position: relative;
}

.logo:hover {
  transform: scale(1.05);
  color: var(--text-secondary);
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--text-primary) !important;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  position: relative;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary) !important;
  background: var(--bg-secondary);
}

.nav-link.active {
  background: var(--bg-tertiary);
}

/* Header controls container */
.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Theme toggle button */
.theme-toggle {
  background: var(--bg-glass);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: var(--bg-secondary);
  transform: scale(1.1);
}

.theme-icon {
  font-size: 1.2rem;
  transition: var(--transition-normal);
}

[data-theme="dark"] .theme-icon {
  transform: rotate(180deg);
}

[data-theme="dark"] .theme-icon::before {
  content: '☀️';
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  margin: 3px 0;
  transition: 0.3s;
}

/*
===============================================
4. BUTTONS AND INTERACTIVE ELEMENTS
===============================================
*/

/* Buttons - Modern gradient buttons with hover effects */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--color-black);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: var(--color-gray-800);
}

.btn-secondary {
  background: var(--color-gray-600);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: var(--color-gray-700);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-medium);
}

.btn-outline:hover {
  background: var(--color-black);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-cta {
  background: var(--color-black);
  color: var(--color-white);
  font-size: 18px;
  padding: 16px 32px;
  box-shadow: var(--shadow-md);
  font-weight: 700;
}

.btn-cta:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  background: var(--color-gray-800);
}

.btn-emergency {
  background: var(--color-gray-800);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.btn-emergency:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: var(--color-gray-900);
}

/*
===============================================
5. HERO SECTIONS
===============================================
*/

/* Hero Section - Main landing area with clean design */
.hero {
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 120px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid var(--border-light);
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: var(--text-primary);
  animation: slideInUp 1s ease-out;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-weight: 600;
  animation: slideInUp 1s ease-out 0.2s both;
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
  line-height: 1.7;
  animation: slideInUp 1s ease-out 0.4s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-hero {
  background: var(--bg-secondary);
  color: var(--text-primary);
  padding: 60px 0;
  text-align: center;
  border-bottom: 1px solid var(--border-light);
}

.page-hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.page-hero p {
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto;
  color: var(--text-secondary);
}

.responsible-hero {
  background: var(--bg-tertiary);
}

.cookie-hero {
  background: var(--bg-tertiary);
}

.hero-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/*
===============================================
6. CONTENT SECTIONS
===============================================
*/


  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 600;
}





.section-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.7;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background: var(--bg-card);
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: var(--shadow-md);
  text-align: center;
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-black);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  background: var(--bg-card);
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: inline-block;
  animation: bounce 2s infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.1rem;
}



.jackpots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.jackpot-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: 2rem;
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.jackpot-card.featured {
  background: var(--secondary-gradient);
  color: var(--text-primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.jackpot-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.jackpot-card.featured:hover {
  transform: scale(1.05) translateY(-10px);
}

.jackpot-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.draw-date {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 1.5rem;
}

.jackpot-amount {
  font-size: 3rem;
  font-weight: 800;
  margin: 1rem 0;
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.jackpot-card.featured .jackpot-amount {
  color: var(--text-primary);
  background: none;
  -webkit-text-fill-color: initial;
}

.jackpot-info p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
}



.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.testimonial-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 2rem;
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-content p {
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.author-info h4 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.author-info span {
  color: var(--text-muted);
  font-size: 0.9rem;
}





/* Steps Section */
.steps-section {
  padding: 80px 0;
}

.steps-grid {
  display: grid;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.step-card {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
}

.step-number {
  width: 48px;
  height: 48px;
  background: #2563eb;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.step-content {
  flex-grow: 1;
}

.step-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.step-content p {
  color: var(--text-secondary);
}

.step-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

/* Info Highlight */
.info-highlight {
  padding: 80px 0;
  background: #eff6ff;
  border-top: 4px solid #3b82f6;
  border-bottom: 4px solid #3b82f6;
}

.info-highlight .section-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.info-highlight h2 {
  color: var(--text-primary);
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.info-highlight p {
  color: #1f2937;
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.features-box {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.features-box h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.features-list {
  list-style: none;
  padding: 0;
}

.features-list li {
  padding: 0.5rem 0;
  color: #374151;
}

/* Results Section */
.results-section {
  padding: 80px 0;
}

.results-section h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.results-grid {
  display: grid;
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.result-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.draw-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.draw-info p {
  color: #6b7280;
}

.prize-info {
  text-align: right;
}

.prize-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.prize-amount {
  font-size: 1.125rem;
  font-weight: bold;
  color: var(--text-primary);
}

.prize-amount.jackpot {
  color: var(--text-primary);
}

.result-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.numbers-section h4,
.prize-section h4 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.winning-numbers,
.supplementary-numbers {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.number {
  width: 40px;
  height: 40px;
  background: var(--color-black);
  color: var(--color-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.number::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.number:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-md);
}

.number.supplementary {
  background: var(--color-gray-600);
  box-shadow: var(--shadow-sm);
}

.number.supplementary:hover {
  box-shadow: var(--shadow-md);
}

.supplementary-section p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.prize-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.prize-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.prize-row:last-child {
  border-bottom: none;
}

/* Odds Section */
.odds-section {
  padding: 60px 0;
  background: #f8fafc;
}

.odds-section h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.odds-content {
  max-width: 800px;
  margin: 0 auto;
}

.odds-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.odds-column h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.odds-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.odds-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.odds-row:last-child {
  border-bottom: none;
}

.draw-info-list {
  list-style: none;
  padding: 0;
}

.draw-info-list li {
  padding: 0.5rem 0;
  color: #374151;
}



.responsible-section .section-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.responsible-section h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.responsible-section p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  font-weight: 500;
}

.help-links {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}

.help-links a {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 0.5rem 1rem;
  background: var(--bg-card);
  border-radius: 8px;
  border: 2px solid var(--border-medium);
  transition: all 0.3s ease;
}

.help-links a:hover {
  color: var(--color-white);
  background: var(--color-black);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.help-links span {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 1.1rem;
}

/* Alert Section */
.alert-section {
  padding: 60px 0;
  background: #fef2f2;
  border-left: 4px solid #ef4444;
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.alert-icon {
  font-size: 4rem;
  flex-shrink: 0;
}

.alert-text h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #dc2626;
}

.alert-text p {
  color: #7f1d1d;
  margin-bottom: 0.5rem;
}

/* Help Resources */
.help-resources {
  padding: 80px 0;
}

.help-resources h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.resource-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.resource-icon {
  font-size: 1.5rem;
}

.resource-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.resource-card p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.resource-contact {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.contact-label {
  color: #6b7280;
}

.contact-value {
  font-weight: 600;
  color: var(--text-primary);
}

.resource-link {
  color: #2563eb;
  text-decoration: none;
}

.resource-link:hover {
  color: var(--text-secondary);
  text-decoration: underline;
}

.emergency-contact {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
}

.emergency-contact h3 {
  font-size: 1.25rem;
  font-weight: bold;
  color: #dc2626;
  margin-bottom: 1rem;
}

.emergency-contact p {
  color: #7f1d1d;
  margin-bottom: 1rem;
}

.emergency-number {
  font-size: 2rem;
  font-weight: bold;
  color: #dc2626;
  margin: 1rem 0;
}

.emergency-note {
  font-size: 0.875rem;
  color: #991b1b;
}

/* Warning Signs */
.warning-signs {
  padding: 80px 0;
  background: #f8fafc;
}

.warning-signs h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.warning-signs .section-description {
  text-align: center;
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 3rem;
}

.warning-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
}

.warning-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.warning-icon {
  color: #ea580c;
  flex-shrink: 0;
}

.warning-cta {
  text-align: center;
}

.warning-cta p {
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 1.5rem;
}

/* Self Help */
.self-help {
  padding: 80px 0;
}

.self-help h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.tool-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.tool-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.tool-card ul {
  list-style: none;
  padding: 0;
}

.tool-card li {
  padding: 0.5rem 0;
  color: #374151;
}

/* Age Verification */
.age-verification {
  padding: 60px 0;
  background: #eff6ff;
}

.age-verification h2 {
  text-align: center;
  font-size: 2.25rem;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.age-verification .section-description {
  text-align: center;
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.commitment-box {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.commitment-box h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.commitment-box ul {
  list-style: none;
  padding: 0;
}

.commitment-box li {
  padding: 0.5rem 0;
  color: #374151;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: #1f2937;
  color: white;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.25rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.cta-section p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
}

/* Legal Pages */
.legal-page {
  padding: 80px 0;
}

.legal-content {
  max-width: 800px;
  margin: 0 auto;
}

.legal-content h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  color: #1f2937;
}

.last-updated {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 3rem;
}

.legal-section {
  margin-bottom: 3rem;
}

.legal-section h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1f2937;
}

.legal-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  margin-top: 2rem;
  color: #374151;
}

.legal-section p {
  margin-bottom: 1rem;
  color: #374151;
  line-height: 1.7;
}

.legal-section ul {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.legal-section li {
  margin-bottom: 0.5rem;
  color: #374151;
  line-height: 1.6;
}

.legal-section a {
  color: #2563eb;
  text-decoration: none;
}

.legal-section a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Legal Page Boxes - Removed duplicate styles, using main .important-notice styles */

.highlight-box {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.highlight-box p {
  color: #1e40af;
  font-weight: 600;
  margin: 0;
}

.warning-box {
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.warning-box p {
  color: #92400e;
  font-weight: 600;
  margin: 0;
}

.important-reminders {
  background: #f3f4f6;
  padding: 2rem;
  border-radius: 12px;
  margin-top: 3rem;
}

.important-reminders h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1f2937;
}

.important-reminders ul {
  list-style: none;
  padding: 0;
}

.important-reminders li {
  padding: 0.5rem 0;
  color: #374151;
}

/* Cookie Policy Specific */
.cookie-types {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cookie-type-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.cookie-type-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.cookie-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.cookie-info {
  flex-grow: 1;
}

.cookie-info h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.cookie-info p {
  color: #6b7280;
  margin: 0;
}

.cookie-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  flex-shrink: 0;
}

.cookie-status.required {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.cookie-status.optional {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.cookie-examples h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.cookie-examples ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cookie-examples li {
  font-size: 0.875rem;
  color: #6b7280;
  padding: 0.25rem 0;
}

/* Age Restriction Page */
.age-restriction-page {
  min-height: 100vh;
  background: #fef2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.restriction-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  text-align: center;
  max-width: 600px;
}

.restriction-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.restriction-content h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #dc2626;
  margin-bottom: 1.5rem;
}

.restriction-message {
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 1.5rem;
}

.restriction-notice {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.restriction-notice p {
  color: #dc2626;
  font-weight: 600;
  margin: 0;
}

.help-section {
  margin-top: 2rem;
}

.help-section p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.help-resources {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.help-resources h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 1rem;
}

.help-resources ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-resources li {
  font-size: 0.875rem;
  color: #1e40af;
  padding: 0.25rem 0;
}

.return-link {
  margin-top: 2rem;
}

/* Footer */
.footer {
  background: var(--color-black);
  color: var(--color-white);
  padding: 60px 0 20px;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-column h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.footer-column p {
  color: var(--color-gray-300);
  font-size: 0.875rem;
  line-height: 1.6;
}

.footer-column ul {
  list-style: none;
  padding: 0;
}

.footer-column li {
  margin-bottom: 0.5rem;
}

.footer-column a {
  color: var(--color-gray-300);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s;
}

.footer-column a:hover {
  color: var(--color-white);
}

/* Footer Regulators Section */
.footer-regulators {
  text-align: center;
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid #374151;
  border-bottom: 1px solid #374151;
}

.footer-regulators h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
}

/* Legacy support for old class name */
.regulator-section {
  text-align: center;
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid #374151;
  border-bottom: 1px solid #374151;
}

.regulator-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
}

.regulator-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.regulator-icon {
  height: 60px;
  width: auto;
  max-width: 120px;
  object-fit: contain;
  transition: all var(--transition-normal);
  border-radius: 8px;
  background: white;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.regulator-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: #f8f9fa;
}

.footer-bottom {
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: var(--color-gray-300);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.cookie-settings {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #374151;
}

.modal-buttons {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
}

.modal-buttons .btn {
  flex: 1;
}

.modal-disclaimer {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  padding: 0 2rem 1.5rem;
}

.modal-disclaimer a {
  color: #2563eb;
  text-decoration: none;
}

.modal-disclaimer a:hover {
  text-decoration: underline;
}

/* Age Gate Modal */
.age-gate {
  padding: 2rem;
}

.age-gate-header {
  text-align: center;
  margin-bottom: 2rem;
}

.age-icon {
  width: 64px;
  height: 64px;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.age-gate-header h2 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.age-gate-header p {
  color: #6b7280;
}

.age-gate form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.age-gate label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 0.5rem;
}

.date-inputs select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 1rem;
  background: white;
}

.date-inputs select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-black);
  border-color: var(--color-black);
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.75rem;
  color: #dc2626;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.warning-box {
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: 6px;
  padding: 0.75rem;
  color: #92400e;
  font-size: 0.875rem;
  margin: 1rem 0;
}

.disclaimer {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  margin-top: 1.5rem;
}

.disclaimer a {
  color: #2563eb;
  text-decoration: none;
}

.disclaimer a:hover {
  text-decoration: underline;
}

/* Cookie Popup - Small popup in bottom right (Index page only) */
.cookie-popup {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 9999;
  max-width: 320px;
  width: calc(100vw - 40px);
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: none !important; /* Hidden by default */
}

.cookie-popup.show {
  display: block !important;
  transform: translateY(0);
  opacity: 1;
}

[data-theme="dark"] .cookie-popup {
  background: var(--color-black);
  border: 1px solid var(--border-light);
}

.cookie-popup-content {
  padding: 1rem;
}

.cookie-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.cookie-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.cookie-icon {
  font-size: 1rem;
}

.cookie-close {
  background: none;
  border: none;
  font-size: 1.1rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-normal);
}

.cookie-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-primary);
}

.cookie-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.cookie-popup-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.cookie-popup-buttons .btn {
  flex: 1;
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-weight: 500;
}

.cookie-popup-buttons .btn-sm {
  padding: 0.35rem 0.7rem;
  font-size: 0.75rem;
}

.cookie-popup-buttons .btn-primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
}

.cookie-popup-buttons .btn-outline {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.cookie-popup-buttons .btn-outline:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.cookie-learn-more {
  display: block;
  text-align: center;
  font-size: 0.75rem;
  color: var(--primary-color);
  text-decoration: none;
  margin-top: 0.25rem;
}

.cookie-learn-more:hover {
  text-decoration: underline;
}
  border: 1px solid var(--text-muted);
  color: var(--text-secondary);
}

.cookie-buttons .btn-secondary:hover {
  background: var(--text-muted);
  color: white;
}

/* Cookie Preferences Section */
.cookie-preferences-section {
  background: var(--color-black);
  padding: 80px 0;
  color: white;
}

.cookie-preferences-content {
  max-width: 900px;
  margin: 0 auto;
}

.preferences-header {
  text-align: center;
  margin-bottom: 3rem;
}

.preferences-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.preferences-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.cookie-settings-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.cookie-setting-card {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 2rem;
  transition: var(--transition-normal);
}

.cookie-setting-card:hover {
  background: var(--bg-secondary);
  transform: translateY(-2px);
}

.cookie-setting-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.cookie-setting-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
}

.cookie-setting-info p {
  font-size: 0.95rem;
  opacity: 0.8;
  margin: 0;
}

.cookie-toggle {
  flex-shrink: 0;
}

.always-active {
  background: var(--bg-secondary);
  color: var(--secondary-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid var(--secondary-color);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-300);
  transition: var(--transition-normal);
  border-radius: 30px;
  border: 1px solid var(--border-light);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-normal);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background: var(--secondary-gradient);
  border-color: var(--secondary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(30px);
}

.cookie-setting-details {
  font-size: 0.95rem;
  line-height: 1.6;
  opacity: 0.9;
}

.cookie-examples {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
  font-size: 0.9rem;
}

.cookie-examples strong {
  color: var(--secondary-color);
}

.cookie-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.cookie-status-message {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  color: #86efac;
}

.cookie-status-message p {
  margin: 0;
  font-weight: 600;
}

/* Cookie Settings */
.cookie-categories {
  padding: 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cookie-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.category-header > div:first-child {
  flex-grow: 1;
}

.category-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.category-header p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.always-active {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.category-description {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-400);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--color-black);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Responsive Design */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .hero {
    padding: 100px 0;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .feature-card {
    padding: 2rem;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-black);
    flex-direction: column;
    padding: 1.5rem;
    gap: 1rem;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .nav-menu.active {
    display: flex;
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-toggle {
    display: flex;
  }

  .nav-actions-desktop {
    display: none;
  }

  .nav-actions-mobile {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
  }

  .nav-actions-mobile .btn {
    width: 100%;
    text-align: center;
    padding: 0.75rem;
    font-weight: 600;
  }

  .hero {
    padding: 80px 0;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .page-hero h1 {
    font-size: 2.25rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .feature-card {
    padding: 2rem;
  }

  .btn-cta {
    font-size: 16px;
    padding: 14px 28px;
  }

  .result-content {
    grid-template-columns: 1fr;
  }

  .odds-grid {
    grid-template-columns: 1fr;
  }

  .resources-grid {
    grid-template-columns: 1fr;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .warning-grid {
    grid-template-columns: 1fr;
  }

  .footer-grid {
    grid-template-columns: 1fr;
  }

  .regulator-icons {
    gap: 1rem;
  }

  .regulator-icon {
    height: 50px;
    max-width: 100px;
  }

  .cookie-preferences-section {
    padding: 60px 0;
  }

  .preferences-header h2 {
    font-size: 2rem;
  }

  .cookie-setting-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .cookie-actions {
    flex-direction: column;
    align-items: center;
  }

  .cookie-actions .btn {
    width: 100%;
    max-width: 300px;
  }

  .cookie-popup {
    bottom: 10px;
    right: 10px;
    max-width: 300px;
  }

  .cookie-popup-content {
    padding: 0.8rem;
  }

  .cookie-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .cookie-buttons .btn {
    flex: none;
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .prize-info {
    text-align: left;
  }

  .alert-content {
    flex-direction: column;
    text-align: center;
  }

  .help-links {
    justify-content: center;
  }

  .modal-buttons {
    flex-direction: column;
  }

  .category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Large mobile devices */
@media (max-width: 640px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .section-content h2 {
    font-size: 2rem;
  }

  .feature-icon {
    font-size: 3.5rem;
  }

  .btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .btn-cta {
    font-size: 16px;
    padding: 12px 24px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .hero {
    padding: 60px 0;
  }

  .hero-content h1 {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-description {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .page-hero h1 {
    font-size: 1.75rem;
  }

  .section-content h2 {
    font-size: 1.75rem;
  }

  .legal-content h1 {
    font-size: 2rem;
  }

  .feature-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .feature-icon {
    font-size: 3rem;
  }

  .step-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .winning-numbers,
  .supplementary-numbers {
    justify-content: center;
  }

  .cookie-popup {
    bottom: 5px;
    right: 5px;
    max-width: 280px;
  }

  .cookie-popup-content {
    padding: 0.6rem;
  }

  .cookie-title {
    font-size: 0.9rem;
  }

  .cookie-text p {
    font-size: 0.8rem;
  }

  .cookie-buttons .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .modal-content {
    margin: 0.5rem;
  }

  .age-gate {
    padding: 1.5rem;
  }

  .restriction-content {
    padding: 2rem;
  }

  .btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .btn-cta {
    font-size: 14px;
    padding: 10px 20px;
  }

  .info-section {
    padding: 60px 0;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .container {
    padding: 0 12px;
  }

  .hero {
    padding: 40px 0;
  }

  .hero-content h1 {
    font-size: 1.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .feature-card {
    padding: 1rem;
  }

  .feature-icon {
    font-size: 2.5rem;
  }

  .btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .btn-cta {
    font-size: 13px;
    padding: 8px 16px;
  }

  .modal-content {
    margin: 0.25rem;
  }

  .age-gate {
    padding: 1rem;
  }
}

/* Print Styles */
@media print {
  .age-badge,
  .cookie-popup,
  .modal-overlay,
  .nav-toggle {
    display: none !important;
  }

  .header {
    position: static;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .container {
    max-width: none;
    padding: 0;
  }

  .hero,
  .page-hero {
    background: none !important;
    color: #000 !important;
  }

  .btn {
    border: 1px solid #000 !important;
    background: none !important;
    color: #000 !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .btn-primary {
    background: #000;
    color: #fff;
    border: 2px solid #000;
  }

  .btn-outline {
    border: 2px solid #000;
    color: #000;
  }

  .feature-card,
  .result-card,
  .resource-card {
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
button:focus,
a:focus,
select:focus,
input:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/*
===============================================
ACCESSIBILITY STYLES
===============================================
*/

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--text-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced focus styles */
*:focus {
  outline: 3px solid var(--secondary-color);
  outline-offset: 2px;
}

/* High contrast focus for buttons */
.btn:focus {
  outline: 3px solid var(--secondary-color);
  outline-offset: 3px;
}

/* Keyboard navigation improvements */
.nav-link:focus,
.theme-toggle:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
  background: var(--bg-secondary);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Additional Mobile Responsiveness Fixes */
@media (max-width: 768px) {
  /* Responsible section mobile fixes */
  .responsible-section {
    padding: 40px 0;
    overflow-x: hidden;
  }

  .responsible-section .container {
    padding: 0 1rem;
    max-width: 100%;
    overflow-x: hidden;
  }

  .responsible-section h3 {
    font-size: 1.5rem;
    line-height: 1.3;
    word-wrap: break-word;
  }

  .responsible-section p {
    font-size: 1rem;
    line-height: 1.5;
    word-wrap: break-word;
  }

  .help-links {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    text-align: center;
  }

  .help-links a {
    font-size: 1rem;
    padding: 0.75rem;
    word-wrap: break-word;
    white-space: normal;
  }

  .help-links span {
    font-size: 1rem;
    display: block;
    margin: 0.5rem 0;
  }

  /* Responsible banner mobile fixes */
  .responsible-banner {
    overflow-x: hidden;
  }

  .responsible-banner .container {
    padding: 0 1rem;
    max-width: 100%;
  }

  .banner-content {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
    font-size: 0.85rem;
  }

  .banner-content span {
    word-wrap: break-word;
    line-height: 1.4;
  }

  /* General mobile overflow fixes */
  body {
    overflow-x: hidden;
  }

  .container {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    overflow-x: hidden;
  }

  /* Text wrapping for long content */
  h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    hyphens: auto;
  }

  p, span, div {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Prevent horizontal scroll on mobile */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Fix for specific problematic elements */
  .mandatory-statements,
  .statement-box,
  .faq-notice,
  .important-notice,
  .key-message,
  .alert-content {
    max-width: 100%;
    overflow-x: hidden;
    word-wrap: break-word;
  }

  /* Ensure tables don't overflow */
  .cookie-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Fix for long URLs and text */
  a {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Navigation mobile fixes */
  .nav-menu {
    max-width: 100%;
    overflow-x: hidden;
  }

  .nav-actions-mobile .btn {
    white-space: normal;
    word-wrap: break-word;
  }
}

/* Mass color replacement for black and white theme */

/* Replace all remaining colored values with black and white equivalents */
.step-content h3,
.info-highlight h2,
.info-highlight p,
.testimonial-author h4,
.testimonial-meta,
.jackpot-card h3,
.draw-info h3,
.prize-amount,
.prize-amount.jackpot,
.winning-numbers h4,
.alert-text h3,
.resource-card h3,
.contact-value,
.resource-link,
.emergency-contact h3,
.emergency-contact p,
.emergency-phone,
.emergency-disclaimer,
.content-section h2,
.content-section h3,
.content-section p,
.warning-text,
.faq-question h3,
.faq-answer,
.security-section h2,
.security-section p,
.security-card h3,
.cookie-info h4,
.cookie-info p,
.badge-text,
.cookie-category,
.cookie-description,
.age-restriction h2,
.restriction-notice p,
.help-section p,
.help-contact h4,
.help-contact p,
.modal-title,
.modal-close,
.modal-disclaimer,
.age-gate-header h2,
.age-gate-header p,
.birth-date-label,
.age-error,
.warning-message,
.disclaimer,
.category-header h4,
.category-header p,
.cookie-toggle-label,
.cookie-description {
  color: var(--text-primary) !important;
}

/* Secondary text colors */
.step-content p,
.draw-info p,
.prize-label,
.supplementary-section p,
.resource-card p,
.contact-label,
.content-section li,
.warning-icon,
.faq-answer p,
.cookie-category,
.cookie-description,
.modal-close,
.age-gate-header p,
.disclaimer {
  color: var(--text-secondary) !important;
}

/* Links */
.resource-link,
.legal-section a,
.modal-disclaimer a,
.disclaimer a {
  color: var(--text-primary) !important;
}

.resource-link:hover,
.legal-section a:hover,
.modal-disclaimer a:hover,
.disclaimer a:hover {
  color: var(--text-secondary) !important;
}

/* Backgrounds */
.highlight-box,
.warning-box,
.emergency-contact,
.age-error,
.warning-message {
  background: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Special elements */
.warning-icon {
  color: var(--text-secondary) !important;
}

.success-indicator {
  color: var(--text-primary) !important;
}

.error-indicator {
  color: var(--text-primary) !important;
}

/* Additional header button visibility fixes */
header .nav-actions .btn,
.header .nav-actions .btn,
nav .nav-actions .btn {
  background-color: var(--color-black) !important;
  color: var(--color-white) !important;
  border: 2px solid var(--color-black) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

header .nav-actions .btn-outline,
.header .nav-actions .btn-outline,
nav .nav-actions .btn-outline {
  background-color: var(--color-white) !important;
  color: var(--color-black) !important;
  border: 2px solid var(--color-black) !important;
}

header .nav-actions .btn:hover,
.header .nav-actions .btn:hover,
nav .nav-actions .btn:hover {
  background-color: var(--color-gray-800) !important;
  color: var(--color-white) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

header .nav-actions .btn-outline:hover,
.header .nav-actions .btn-outline:hover,
nav .nav-actions .btn-outline:hover {
  background-color: var(--color-black) !important;
  color: var(--color-white) !important;
}

/* Ultra-specific styles for navigation buttons */
.nav-actions-desktop .btn.btn-outline,
.nav-actions-desktop .btn.btn-primary,
.nav-actions-desktop a.btn {
  background-color: var(--color-black) !important;
  color: var(--color-white) !important;
  border: 2px solid var(--color-black) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  min-width: 70px !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.nav-actions-desktop .btn.btn-outline {
  background-color: var(--color-white) !important;
  color: var(--color-black) !important;
  border: 2px solid var(--color-black) !important;
}

.nav-actions-desktop .btn:hover {
  background-color: var(--color-gray-800) !important;
  color: var(--color-white) !important;
  border-color: var(--color-gray-800) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.nav-actions-desktop .btn.btn-outline:hover {
  background-color: var(--color-black) !important;
  color: var(--color-white) !important;
  border-color: var(--color-black) !important;
}

/* Emergency fix for button visibility - maximum contrast */
.header .nav-actions-desktop .btn,
.header .nav-actions .btn,
header .btn {
  background: #000000 !important;
  color: #ffffff !important;
  border: 3px solid #000000 !important;
  font-weight: 700 !important;
  font-size: 14px !important;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  text-decoration: none !important;
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  position: relative !important;
  z-index: 999 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.header .nav-actions-desktop .btn.btn-outline,
.header .nav-actions .btn.btn-outline,
header .btn.btn-outline {
  background: #ffffff !important;
  color: #000000 !important;
  border: 3px solid #000000 !important;
}

.header .nav-actions-desktop .btn:hover,
.header .nav-actions .btn:hover,
header .btn:hover {
  background: #333333 !important;
  color: #ffffff !important;
  border-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

.header .nav-actions-desktop .btn.btn-outline:hover,
.header .nav-actions .btn.btn-outline:hover,
header .btn.btn-outline:hover {
  background: #000000 !important;
  color: #ffffff !important;
  border-color: #000000 !important;
}

/* Force navigation links visibility */
.nav-menu .nav-link,
.nav .nav-link,
header .nav-link,
.header .nav-link {
  color: var(--text-primary) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-sm) !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  text-decoration: none !important;
  border-radius: var(--radius-md) !important;
}

.nav-menu .nav-link:hover,
.nav .nav-link:hover,
header .nav-link:hover,
.header .nav-link:hover,
.nav-menu .nav-link.active,
.nav .nav-link.active,
header .nav-link.active,
.header .nav-link.active {
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary) !important;
}

/* Countdown Timer Styles */
.countdown-container {
  text-align: center;
  margin: var(--spacing-2xl) 0;
}

.countdown-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}



/* Responsive countdown */
@media (max-width: 768px) {
  .countdown-timer {
    gap: var(--spacing-sm);
  }

  .countdown-unit {
    min-width: 50px;
  }

  .countdown-number {
    font-size: var(--font-size-xl);
    padding: var(--spacing-sm) var(--spacing-md);
    min-width: 50px;
  }

  .countdown-separator {
    font-size: var(--font-size-xl);
    margin: 0 var(--spacing-xs);
  }
}




