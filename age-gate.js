// Age Gate Functionality
document.addEventListener("DOMContentLoaded", () => {
  const ageGateModal = document.getElementById("ageGateModal")
  const ageVerificationForm = document.getElementById("ageVerificationForm")
  const birthDaySelect = document.getElementById("birthDay")
  const birthYearSelect = document.getElementById("birthYear")
  const ageError = document.getElementById("ageError")

  // Check if age verification is already done
  function checkAgeVerification() {
    const ageVerified = localStorage.getItem("ageVerified")
    if (!ageVerified) {
      showAgeGate()
    } else {
      hideAgeGate()
    }
  }

  // Show age gate modal
  function showAgeGate() {
    if (ageGateModal) {
      ageGateModal.classList.add("show")
      populateSelects()
    }
  }

  // Hide age gate modal
  function hideAgeGate() {
    if (ageGateModal) {
      ageGateModal.classList.remove("show")
    }
  }

  // Populate day and year selects
  function populateSelects() {
    // Populate days
    if (birthDaySelect) {
      for (let i = 1; i <= 31; i++) {
        const option = document.createElement("option")
        option.value = i
        option.textContent = i
        birthDaySelect.appendChild(option)
      }
    }

    // Populate years
    if (birthYearSelect) {
      const currentYear = new Date().getFullYear()
      for (let i = currentYear; i >= currentYear - 100; i--) {
        const option = document.createElement("option")
        option.value = i
        option.textContent = i
        birthYearSelect.appendChild(option)
      }
    }
  }

  // Show error message
  function showError(message) {
    if (ageError) {
      ageError.textContent = message
      ageError.style.display = "block"
    }
  }

  // Hide error message
  function hideError() {
    if (ageError) {
      ageError.style.display = "none"
    }
  }

  // Calculate age
  function calculateAge(birthDate) {
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    return age
  }

  // Handle form submission
  if (ageVerificationForm) {
    ageVerificationForm.addEventListener("submit", (e) => {
      e.preventDefault()
      hideError()

      const day = document.getElementById("birthDay").value
      const month = document.getElementById("birthMonth").value
      const year = document.getElementById("birthYear").value

      // Validate inputs
      if (!day || !month || !year) {
        showError("Please enter your complete date of birth")
        return
      }

      const dayNum = Number.parseInt(day)
      const monthNum = Number.parseInt(month)
      const yearNum = Number.parseInt(year)

      // Basic validation
      if (
        dayNum < 1 ||
        dayNum > 31 ||
        monthNum < 1 ||
        monthNum > 12 ||
        yearNum < 1900 ||
        yearNum > new Date().getFullYear()
      ) {
        showError("Please enter a valid date of birth")
        return
      }

      // Create birth date
      const birthDate = new Date(yearNum, monthNum - 1, dayNum)

      // Check if date is valid
      if (
        birthDate.getDate() !== dayNum ||
        birthDate.getMonth() !== monthNum - 1 ||
        birthDate.getFullYear() !== yearNum
      ) {
        showError("Please enter a valid date of birth")
        return
      }

      // Calculate age
      const age = calculateAge(birthDate)

      // Check if 18 or older
      if (age >= 18) {
        localStorage.setItem("ageVerified", "true")
        localStorage.setItem("ageVerifiedDate", new Date().toISOString())
        hideAgeGate()
      } else {
        // Redirect to age restriction page
        window.location.href = "age-restriction.html"
      }
    })
  }

  // Initialize age verification check
  checkAgeVerification()




})
