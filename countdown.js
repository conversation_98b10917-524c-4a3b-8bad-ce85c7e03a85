/**
 * Countdown Timer for Lottery Draw
 * Displays countdown to next major draw event
 */

class CountdownTimer {
    constructor(targetElementId, targetDate) {
        this.targetElement = document.getElementById(targetElementId);
        this.targetDate = new Date(targetDate);
        this.interval = null;
        
        if (!this.targetElement) {
            console.error('Countdown target element not found:', targetElementId);
            return;
        }
        
        this.init();
    }
    
    init() {
        // Create countdown HTML structure
        this.createCountdownHTML();
        
        // Start the countdown
        this.startCountdown();
        
        // Update immediately
        this.updateCountdown();
    }
    
    createCountdownHTML() {
        const countdownHTML = `
            <div class="countdown-container">
                <div class="countdown-title">Time Until Draw</div>
                <div class="countdown-timer">
                    <div class="countdown-unit">
                        <div class="countdown-number" id="countdown-days">00</div>
                        <div class="countdown-label">Days</div>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-unit">
                        <div class="countdown-number" id="countdown-hours">00</div>
                        <div class="countdown-label">Hours</div>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-unit">
                        <div class="countdown-number" id="countdown-minutes">00</div>
                        <div class="countdown-label">Minutes</div>
                    </div>
                    <div class="countdown-separator">:</div>
                    <div class="countdown-unit">
                        <div class="countdown-number" id="countdown-seconds">00</div>
                        <div class="countdown-label">Seconds</div>
                    </div>
                </div>
                <div class="countdown-status" id="countdown-status"></div>
            </div>
        `;
        
        this.targetElement.innerHTML = countdownHTML;
    }
    
    updateCountdown() {
        const now = new Date().getTime();
        const distance = this.targetDate.getTime() - now;
        
        // Calculate time units
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        // Update display
        const daysEl = document.getElementById('countdown-days');
        const hoursEl = document.getElementById('countdown-hours');
        const minutesEl = document.getElementById('countdown-minutes');
        const secondsEl = document.getElementById('countdown-seconds');
        const statusEl = document.getElementById('countdown-status');
        
        if (distance > 0) {
            // Update countdown numbers with animation
            this.updateNumber(daysEl, days);
            this.updateNumber(hoursEl, hours);
            this.updateNumber(minutesEl, minutes);
            this.updateNumber(secondsEl, seconds);
            
            // Update status
            if (statusEl) {
                if (days > 0) {
                    statusEl.textContent = `Draw in ${days} day${days !== 1 ? 's' : ''}`;
                } else if (hours > 0) {
                    statusEl.textContent = `Draw in ${hours} hour${hours !== 1 ? 's' : ''}`;
                } else if (minutes > 0) {
                    statusEl.textContent = `Draw in ${minutes} minute${minutes !== 1 ? 's' : ''}`;
                } else {
                    statusEl.textContent = `Draw in ${seconds} second${seconds !== 1 ? 's' : ''}`;
                }
            }
        } else {
            // Draw has passed
            this.handleExpiredCountdown();
        }
    }
    
    updateNumber(element, value) {
        if (!element) return;
        
        const formattedValue = value.toString().padStart(2, '0');
        
        if (element.textContent !== formattedValue) {
            element.classList.add('countdown-flip');
            
            setTimeout(() => {
                element.textContent = formattedValue;
                element.classList.remove('countdown-flip');
            }, 150);
        }
    }
    
    handleExpiredCountdown() {
        // Stop the countdown
        this.stopCountdown();
        
        // Update display to show draw has occurred
        const statusEl = document.getElementById('countdown-status');
        if (statusEl) {
            statusEl.textContent = 'Draw has concluded';
            statusEl.classList.add('countdown-expired');
        }
        
        // Set all numbers to 00
        ['countdown-days', 'countdown-hours', 'countdown-minutes', 'countdown-seconds'].forEach(id => {
            const el = document.getElementById(id);
            if (el) el.textContent = '00';
        });
        
        // Optionally set next draw date (7 days later)
        setTimeout(() => {
            this.setNextDrawDate();
        }, 5000);
    }
    
    setNextDrawDate() {
        // Set next draw to next Saturday 8:30 PM
        const nextDraw = this.getNextSaturday();
        this.targetDate = nextDraw;
        
        // Update the draw date display
        const drawDateEl = document.getElementById('drawDate');
        if (drawDateEl) {
            drawDateEl.textContent = this.formatDrawDate(nextDraw);
        }
        
        // Restart countdown
        this.startCountdown();
        
        // Remove expired status
        const statusEl = document.getElementById('countdown-status');
        if (statusEl) {
            statusEl.classList.remove('countdown-expired');
        }
    }
    
    getNextSaturday() {
        const now = new Date();
        const nextSaturday = new Date();
        
        // Find next Saturday
        const daysUntilSaturday = (6 - now.getDay()) % 7;
        const daysToAdd = daysUntilSaturday === 0 ? 7 : daysUntilSaturday;
        
        nextSaturday.setDate(now.getDate() + daysToAdd);
        nextSaturday.setHours(20, 30, 0, 0); // 8:30 PM
        
        return nextSaturday;
    }
    
    formatDrawDate(date) {
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        return date.toLocaleDateString('en-AU', options);
    }
    
    startCountdown() {
        // Clear any existing interval
        this.stopCountdown();
        
        // Start new interval
        this.interval = setInterval(() => {
            this.updateCountdown();
        }, 1000);
    }
    
    stopCountdown() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }
    
    destroy() {
        this.stopCountdown();
        if (this.targetElement) {
            this.targetElement.innerHTML = '';
        }
    }
}

// Initialize countdown when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set target date to next Saturday 8:30 PM AEST
    const getNextSaturday = () => {
        const now = new Date();
        const nextSaturday = new Date();
        
        // Find next Saturday
        const daysUntilSaturday = (6 - now.getDay()) % 7;
        const daysToAdd = daysUntilSaturday === 0 ? 7 : daysUntilSaturday;
        
        nextSaturday.setDate(now.getDate() + daysToAdd);
        nextSaturday.setHours(20, 30, 0, 0); // 8:30 PM
        
        return nextSaturday;
    };
    
    const targetDate = getNextSaturday();
    
    // Initialize countdown timer
    const countdown = new CountdownTimer('countdown-timer', targetDate);
    
    // Update the draw date display
    const drawDateEl = document.getElementById('drawDate');
    if (drawDateEl) {
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        drawDateEl.textContent = targetDate.toLocaleDateString('en-AU', options);
    }
});
