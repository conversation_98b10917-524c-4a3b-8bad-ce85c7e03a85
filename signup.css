/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    color: #000;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #000;
}

/* Main Content */
.main {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

.signup-container {
    max-width: 600px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 2rem;
}

.signup-header {
    text-align: center;
    margin-bottom: 2rem;
}

.signup-header h1 {
    font-size: 2rem;
    color: #000;
    margin-bottom: 0.5rem;
}

.signup-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Form Styles */
.signup-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #000;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
}

.checkbox-label a {
    color: #000;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* Submit Button */
.btn-submit {
    background: #000;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 1rem;
}

.btn-submit:hover {
    background: #333;
}

.login-link {
    text-align: center;
    margin-top: 1rem;
}

.login-link a {
    color: #000;
    text-decoration: none;
    font-weight: 600;
}

.login-link a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 2rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #fbbf24;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.footer-section p {
    font-size: 0.9rem;
    line-height: 1.5;
}

.regulators {
    text-align: center;
    margin-bottom: 1.5rem;
}

.regulators h4 {
    color: #fbbf24;
    margin-bottom: 1rem;
}

.regulator-icons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.regulator-icon {
    height: 40px;
    width: auto;
    transition: transform 0.3s;
}

.regulator-icon:hover {
    transform: scale(1.1);
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #374151;
    font-size: 0.85rem;
    color: #9ca3af;
}

/* Mobile Navigation Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #000;
    margin: 3px 0;
    transition: 0.3s;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: #fff;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .nav-link {
        margin: 1rem 0;
        font-size: 1.2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .signup-container {
        margin: 0 1rem;
        padding: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .regulator-icons {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .signup-header h1 {
        font-size: 1.5rem;
    }
    
    .signup-container {
        padding: 1rem;
    }
    
    .btn-submit {
        padding: 0.75rem 1.5rem;
    }
}
